// src/middleware.ts
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

// Configuración de rutas y permisos
const ROUTE_PERMISSIONS = {
  public: [
    '/',
    '/login',
    '/payment',
    '/thank-you',
    '/contact',
    '/privacy',
    '/terms',
    '/auth/callback',
    '/auth/unauthorized',
    '/auth/reset-password',
    '/auth/confirm-reset',
  ],
  apiPublic: [ // Rutas API que no necesitan autenticación de usuario (pueden tener su propia auth)
    '/api/stripe/webhook',
    '/api/stripe/create-checkout-session',
    '/api/notify-signup',
    '/api/user/status',
    '/api/auth/register-free', // Asumiendo que esta es para registro público
  ],
  authenticated: [ // Requieren autenticación básica
    '/app',
    '/dashboard',
    '/profile',
    '/welcome',
    '/upgrade-plan',
    // Las rutas de API para usuarios autenticados se manejan dentro de los propios endpoints
  ],
  planRestricted: { // Requieren plan específico (y autenticación)
    '/plan-estudios': ['pro'],
    '/app/ai-tutor': ['usuario', 'pro'],
    '/app/summaries': ['pro'],
    // Añade más rutas aquí si es necesario
  }
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  console.log(`[MIDDLEWARE] Request a: ${pathname}`);

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options });
          response = NextResponse.next({
            request: { headers: request.headers },
          });
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: '', ...options });
          response = NextResponse.next({
            request: { headers: request.headers },
          });
          response.cookies.set({ name, value: '', ...options });
        },
      },
      auth: {
        // Importante para el middleware: no queremos persistir sesión aquí
        // ni refrescar tokens automáticamente, ya que esto es para proteger rutas.
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: true, // Necesario para el flujo de reseteo de contraseña y PKCE
      }
    }
  );

  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError) {
    console.error('[MIDDLEWARE] Error al obtener usuario:', userError.message);
    // Si hay error obteniendo el usuario, por seguridad, redirigir al login
    // excepto para rutas públicas de API que podrían tener su propia autenticación.
    if (!isApiPublicRoute(pathname)) {
      return redirectToLogin(request, 'auth_error');
    }
  }
  console.log(`[MIDDLEWARE] Usuario: ${user ? user.id : 'null'}`);


  // 1. Rutas de API públicas: permitir siempre (manejan su propia auth si es necesario)
  if (isApiPublicRoute(pathname)) {
    console.log(`[MIDDLEWARE] Ruta API pública permitida: ${pathname}`);
    return addSecurityHeaders(response);
  }

  // 2. Manejo especial para el flujo de reseteo de contraseña
  // Si hay un token de recuperación en la URL, Supabase.auth.getUser() puede devolver un usuario
  // temporalmente. Queremos permitir el acceso a /auth/reset-password en este caso.
  if (user && pathname === '/auth/reset-password' && request.nextUrl.hash.includes('access_token')) {
    console.log(`[MIDDLEWARE] Permitiendo acceso a /auth/reset-password con token de recuperación`);
    return addSecurityHeaders(response);
  }

  // 3. Rutas públicas de UI
  if (isPublicUiRoute(pathname)) {
    console.log(`[MIDDLEWARE] Ruta UI pública detectada: ${pathname}`);
    if (user && (pathname === '/' || pathname === '/login')) {
      console.log(`[MIDDLEWARE] Usuario autenticado en ${pathname}, redirigiendo a /app`);
      return addSecurityHeaders(NextResponse.redirect(new URL('/app', request.url)));
    }
    console.log(`[MIDDLEWARE] Permitiendo acceso a UI pública sin usuario o en ruta no conflictiva`);
    return addSecurityHeaders(response);
  }

  // 4. Si no hay usuario y la ruta no es pública, redirigir al login
  if (!user) {
    console.log(`[MIDDLEWARE] No hay usuario y no es ruta pública: ${pathname}. Redirigiendo a login.`);
    return redirectToLogin(request);
  }

  // --- A partir de aquí, asumimos que `user` existe ---

  // 5. Validación de perfil y plan para rutas autenticadas
  const profileValidation = await validateUserProfile(user.id, supabase);

  if (!profileValidation.valid) {
    console.log(`[MIDDLEWARE] Validación de perfil fallida para ${user.id}: ${profileValidation.reason}`);
    // Si el perfil no es válido (ej. no encontrado, expirado, pago no verificado)
    // Podríamos intentar crear un perfil de recuperación si la razón es "Profile not found"
    // y es un usuario que se registró legítimamente (ej. cuenta gratuita)
    // Esta lógica de `checkIfLegitimateUser` y `createRecoveryProfile` es compleja
    // y puede ser mejor manejarla en la página de destino o en un endpoint específico.
    // Por ahora, redirigimos a una página de error o pago.
    return redirectToPayment(request, profileValidation.reason || 'profile_error');
  }

  // 6. Validación de plan para rutas con restricciones de plan
  if (isPlanRestrictedRoute(pathname)) {
    const planValidation = await validatePlanAccessForRoute(user.id, pathname, supabase);
    if (!planValidation.valid) {
      console.log(`[MIDDLEWARE] Validación de plan fallida para ${pathname} por usuario ${user.id}: ${planValidation.reason}`);
      return redirectToUnauthorized(request, planValidation);
    }
  }

  console.log(`[MIDDLEWARE] Acceso permitido para usuario ${user.id} a ${pathname}`);
  return addSecurityHeaders(response);
}

// --- Funciones Auxiliares ---

function isApiPublicRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.apiPublic.some(path => pathname.startsWith(path));
}

function isPublicUiRoute(pathname: string): boolean {
  return ROUTE_PERMISSIONS.public.some(path => pathname === path);
}

function isPlanRestrictedRoute(pathname: string): boolean {
  return Object.keys(ROUTE_PERMISSIONS.planRestricted).some(path => pathname.startsWith(path));
}

async function validateUserProfile(userId: string, supabase: any): Promise<{ valid: boolean; reason?: string }> {
  try {
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified, plan_expires_at')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return { valid: false, reason: 'Profile not found' };
    }

    if (profile.plan_expires_at) {
      if (new Date() > new Date(profile.plan_expires_at)) {
        return { valid: false, reason: 'Account expired' };
      }
    }

    if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
      return { valid: false, reason: 'Payment not verified' };
    }

    return { valid: true };
  } catch (e) {
    console.error('[MIDDLEWARE] Error en validateUserProfile:', e);
    return { valid: false, reason: 'User profile validation error' };
  }
}

async function validatePlanAccessForRoute(userId: string, pathname: string, supabase: any): Promise<{
  valid: boolean;
  reason?: string;
  requiredPlans?: string[];
  userPlan?: string;
}> {
  const routeConfig = Object.entries(ROUTE_PERMISSIONS.planRestricted)
    .find(([pathPrefix]) => pathname.startsWith(pathPrefix));

  if (!routeConfig) { // No es una ruta con restricción de plan específica
    return { valid: true };
  }

  const requiredPlans = routeConfig[1];

  try {
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return { valid: false, reason: 'Profile not found for plan check', requiredPlans };
    }

    if (!requiredPlans.includes(profile.subscription_plan)) {
      return {
        valid: false,
        reason: `Plan '${profile.subscription_plan}' not sufficient for route ${pathname}`,
        requiredPlans,
        userPlan: profile.subscription_plan
      };
    }
    return { valid: true, userPlan: profile.subscription_plan };
  } catch (e) {
    console.error('[MIDDLEWARE] Error en validatePlanAccessForRoute:', e);
    return { valid: false, reason: 'Plan access validation error', requiredPlans };
  }
}

function redirectToLogin(request: NextRequest, reason?: string): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/login';
  url.searchParams.set('redirectedFrom', request.nextUrl.pathname);
  if (reason) url.searchParams.set('reason', reason);
  console.log(`[MIDDLEWARE] Redirigiendo a login. Razón: ${reason || 'Acceso no autorizado'}`);
  return addSecurityHeaders(NextResponse.redirect(url));
}

function redirectToPayment(request: NextRequest, reason?: string): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/payment'; // O a una página específica de "actualizar pago"
  if (reason) url.searchParams.set('reason', reason);
  console.log(`[MIDDLEWARE] Redirigiendo a payment. Razón: ${reason}`);
  return addSecurityHeaders(NextResponse.redirect(url));
}

function redirectToUnauthorized(request: NextRequest, validationResult: { reason?: string; requiredPlans?: string[]; userPlan?: string }): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = '/auth/unauthorized';
  url.searchParams.set('feature', request.nextUrl.pathname);
  if (validationResult.reason) url.searchParams.set('reason', validationResult.reason);
  if (validationResult.requiredPlans) url.searchParams.set('required_plan', validationResult.requiredPlans.join(','));
  if (validationResult.userPlan) url.searchParams.set('current_plan', validationResult.userPlan);
  console.log(`[MIDDLEWARE] Redirigiendo a unauthorized. Razón: ${validationResult.reason}`);
  return addSecurityHeaders(NextResponse.redirect(url));
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  // response.headers.set('X-XSS-Protection', '1; mode=block'); // Obsoleto en navegadores modernos, CSP es preferido
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co https://api.stripe.com ws:;"
  ); // Añadido font-src y ws: (para HMR de Next.js en dev)
  return response;
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)',
  ],
};