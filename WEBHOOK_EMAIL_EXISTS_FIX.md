# Fix para el Error email_exists en Webhooks de Stripe

## Problema Identificado

El error `email_exists` ocurría cuando un usuario que ya tenía una cuenta en Supabase Auth (por ejemplo, de un registro gratuito anterior o un intento de pago fallido) intentaba comprar un plan. El flujo actual no manejaba correctamente este escenario.

## Causa Raíz

- El método `createUserWithInvitation` intentaba crear un nuevo usuario en Supabase Auth
- Si el email ya existía, fallaba con error `email_exists`
- El webhook no tenía lógica para manejar usuarios existentes por email
- Solo verificaba por `stripe_customer_id`, no por email

## Solución Implementada

### 1. Actualización de `UserManagementService.createUserWithPlan`

**Archivo:** `src/lib/services/userManagement.ts`

**Cambios principales:**
- Agregado manejo de excepción para error `email_exists`
- Cuando ocurre el error, obtiene el usuario existente por email
- Usa el `user_id` existente en lugar de crear uno nuevo
- Actualiza el perfil existente con los nuevos datos del plan
- Mantiene `security_flags` existentes al hacer merge

**Código clave:**
```typescript
try {
  userInvitation = await SupabaseAdminService.createUserWithInvitation(
    request.email,
    userData
  );
  createdUserId = userInvitation.user!.id;
} catch (invitationError: any) {
  if (invitationError.message && invitationError.message.includes('A user with this email address has already been registered')) {
    console.warn('⚠️ Usuario ya existe con este email. Intentando actualizar plan...');
    
    const existingUser = await SupabaseAdminService.getUserByEmail(request.email);
    if (!existingUser) {
      throw new Error('Error obteniendo usuario existente.');
    }
    createdUserId = existingUser.id;
  } else {
    throw invitationError;
  }
}
```

### 2. Actualización de `StripeWebhookHandlers.handleCheckoutSessionCompleted`

**Archivo:** `src/lib/services/stripeWebhookHandlers.ts`

**Cambios principales:**
- Agregado manejo de error `email_exists` en el webhook
- Flujo de actualización para usuarios existentes
- Actualización de `stripe_customer_id` y `stripe_subscription_id`
- Asociación correcta de transacciones con usuarios existentes

**Código clave:**
```typescript
if (result.error?.includes('A user with this email address has already been registered') || 
    result.error?.includes('email_exists')) {
  
  const existingUser = await SupabaseAdminService.getUserByEmail(customerEmail);
  const updateResult = await UserManagementService.updateUserPlan(
    existingUser.id,
    planId,
    undefined,
    'New payment for existing user email'
  );
  
  // Actualizar stripe_customer_id y stripe_subscription_id
  await supabaseAdmin.from('user_profiles').update({
    stripe_customer_id: session.customer as string,
    stripe_subscription_id: subscriptionId,
    payment_verified: true
  }).eq('user_id', existingUser.id);
}
```

### 3. Nuevo método auxiliar en `SupabaseAdminService`

**Archivo:** `src/lib/supabase/admin.ts`

**Método agregado:**
```typescript
static async getUserByEmail(email: string): Promise<{ id: string; email: string; email_confirmed_at?: string } | null> {
  const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers({ email });
  
  if (error || !users || users.length === 0) {
    return null;
  }
  
  return {
    id: users[0].id,
    email: users[0].email!,
    email_confirmed_at: users[0].email_confirmed_at
  };
}
```

## Flujo Mejorado

### Escenario 1: Usuario Nuevo
1. `createUserWithInvitation` → ✅ Éxito
2. Crear perfil con nuevo `user_id`
3. Procesar pago normalmente

### Escenario 2: Usuario Existente (email_exists)
1. `createUserWithInvitation` → ❌ Error `email_exists`
2. Capturar excepción y obtener usuario existente por email
3. Usar `user_id` existente
4. Actualizar/crear perfil con datos del nuevo plan
5. Marcar como `payment_verified: true`
6. Asociar transacción con usuario existente

## Beneficios

1. **Eliminación del bucle infinito** en `/thank-you`
2. **Manejo correcto de usuarios existentes** que compran planes
3. **Preservación de datos existentes** (security_flags, etc.)
4. **Transacciones correctamente asociadas** a usuarios
5. **Logs mejorados** para debugging

## Testing

Se agregaron tests unitarios en `src/lib/services/__tests__/userManagement.test.ts` para verificar:
- Manejo correcto del error `email_exists`
- Actualización de usuarios existentes
- Creación de usuarios nuevos
- Manejo de otros tipos de errores

## Consideraciones de Seguridad

- ✅ Verificación de email antes de actualizar perfil
- ✅ Preservación de `security_flags` existentes
- ✅ Validación de transacciones para evitar duplicados
- ✅ Logs detallados para auditoría

## Próximos Pasos

1. **Monitorear logs** para verificar que el fix funciona
2. **Probar escenarios edge case** como:
   - Usuario con cuenta pero sin perfil
   - Usuario con perfil pero cuenta desactivada
   - Múltiples intentos de pago simultáneos
3. **Considerar notificaciones por email** para usuarios existentes que actualizan plan
