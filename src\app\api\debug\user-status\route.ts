// src/app/api/debug/user-status/route.ts
// Endpoint de debug para verificar el estado del usuario

import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService, supabaseAdmin } from '@/lib/supabase/admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    
    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 });
    }

    console.log('🔍 Debug: Verificando estado del usuario:', email);

    // 1. Verificar usuario en Supabase Auth
    const authUser = await SupabaseAdminService.getUserByEmail(email);
    console.log('👤 Usuario en Auth:', authUser);

    if (!authUser) {
      return NextResponse.json({
        email,
        authUser: null,
        profile: null,
        transactions: [],
        message: 'Usuario no encontrado en Auth'
      });
    }

    // 2. Verificar perfil en user_profiles
    const profile = await SupabaseAdminService.getUserProfile(authUser.id);
    console.log('📋 Perfil del usuario:', profile);

    // 3. Verificar transacciones
    const { data: transactions, error: transError } = await supabaseAdmin
      .from('stripe_transactions')
      .select('*')
      .eq('user_email', email)
      .order('created_at', { ascending: false });

    if (transError) {
      console.error('Error obteniendo transacciones:', transError);
    }

    console.log('💳 Transacciones:', transactions);

    // 4. Verificar si hay transacciones por user_id también
    const { data: transactionsByUserId, error: transUserIdError } = await supabaseAdmin
      .from('stripe_transactions')
      .select('*')
      .eq('user_id', authUser.id)
      .order('created_at', { ascending: false });

    if (transUserIdError) {
      console.error('Error obteniendo transacciones por user_id:', transUserIdError);
    }

    console.log('💳 Transacciones por user_id:', transactionsByUserId);

    return NextResponse.json({
      email,
      authUser: {
        id: authUser.id,
        email: authUser.email,
        email_confirmed_at: authUser.email_confirmed_at
      },
      profile: profile ? {
        id: profile.id,
        user_id: profile.user_id,
        subscription_plan: profile.subscription_plan,
        payment_verified: profile.payment_verified,
        stripe_customer_id: profile.stripe_customer_id,
        stripe_subscription_id: profile.stripe_subscription_id,
        last_payment_date: profile.last_payment_date,
        plan_expires_at: profile.plan_expires_at,
        auto_renew: profile.auto_renew,
        monthly_token_limit: profile.monthly_token_limit,
        current_month_tokens: profile.current_month_tokens,
        security_flags: profile.security_flags
      } : null,
      transactions: transactions || [],
      transactionsByUserId: transactionsByUserId || [],
      debug: {
        hasAuthUser: !!authUser,
        hasProfile: !!profile,
        profilePaymentVerified: profile?.payment_verified || false,
        profilePlan: profile?.subscription_plan || 'none',
        transactionCount: transactions?.length || 0,
        transactionsByUserIdCount: transactionsByUserId?.length || 0
      }
    });

  } catch (error) {
    console.error('❌ Error en debug user-status:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    );
  }
}
