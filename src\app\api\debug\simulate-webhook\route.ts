// src/app/api/debug/simulate-webhook/route.ts
// Endpoint para simular el webhook de Stripe y probar el flujo

import { NextRequest, NextResponse } from 'next/server';
import { UserManagementService } from '@/lib/services/userManagement';
import { StripeWebhookHandlers } from '@/lib/services/stripeWebhookHandlers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, planId = 'usuario', simulateEmailExists = false } = body;
    
    if (!email) {
      return NextResponse.json({ error: 'Email parameter required' }, { status: 400 });
    }

    console.log('🧪 Simulando webhook para:', email, 'Plan:', planId);

    // Simular datos de sesión de Stripe
    const mockSession = {
      id: `cs_test_${Date.now()}`,
      customer: `cus_test_${Date.now()}`,
      amount_total: 999,
      currency: 'eur',
      payment_status: 'paid',
      mode: 'subscription',
      subscription: `sub_test_${Date.now()}`,
      metadata: {
        planId: planId,
        customerEmail: email,
        customerName: 'Test User'
      }
    };

    console.log('📋 Datos de sesión simulada:', mockSession);

    if (simulateEmailExists) {
      // Simular directamente el flujo de createUserWithPlan
      console.log('🔄 Simulando createUserWithPlan...');
      
      const result = await UserManagementService.createUserWithPlan({
        email: email,
        name: 'Test User',
        planId: planId,
        stripeSessionId: mockSession.id,
        stripeCustomerId: mockSession.customer,
        amount: mockSession.amount_total,
        currency: mockSession.currency,
        subscriptionId: mockSession.subscription
      });

      console.log('📊 Resultado de createUserWithPlan:', result);

      return NextResponse.json({
        type: 'createUserWithPlan_simulation',
        session: mockSession,
        result: result,
        success: result.success
      });
    } else {
      // Simular el webhook completo
      console.log('🔄 Simulando webhook completo...');
      
      const result = await StripeWebhookHandlers.handleCheckoutSessionCompleted(mockSession as any);

      console.log('📊 Resultado del webhook:', result);

      return NextResponse.json({
        type: 'full_webhook_simulation',
        session: mockSession,
        result: result,
        success: result.success
      });
    }

  } catch (error) {
    console.error('❌ Error en simulate-webhook:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    );
  }
}
