'use client';

import { useState } from 'react';

export default function WebhookDebugPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [planId, setPlanId] = useState('usuario');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [userStatus, setUserStatus] = useState<any>(null);

  const checkUserStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/debug/user-status?email=${encodeURIComponent(email)}`);
      const data = await response.json();
      setUserStatus(data);
    } catch (error) {
      console.error('Error checking user status:', error);
    } finally {
      setLoading(false);
    }
  };

  const simulateWebhook = async (simulateEmailExists = false) => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/simulate-webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          planId,
          simulateEmailExists
        })
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error simulating webhook:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Webhook Debug Tool</h1>
      
      <div className="space-y-6">
        {/* Configuración */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Configuración</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email:</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-2 border rounded"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Plan ID:</label>
              <select
                value={planId}
                onChange={(e) => setPlanId(e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="free">Free</option>
                <option value="usuario">Usuario</option>
                <option value="pro">Pro</option>
              </select>
            </div>
          </div>
        </div>

        {/* Acciones */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Acciones</h2>
          <div className="space-y-2">
            <button
              onClick={checkUserStatus}
              disabled={loading}
              className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Verificando...' : 'Verificar Estado del Usuario'}
            </button>
            <button
              onClick={() => simulateWebhook(false)}
              disabled={loading}
              className="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              {loading ? 'Simulando...' : 'Simular Webhook Completo'}
            </button>
            <button
              onClick={() => simulateWebhook(true)}
              disabled={loading}
              className="w-full bg-orange-500 text-white p-2 rounded hover:bg-orange-600 disabled:opacity-50"
            >
              {loading ? 'Simulando...' : 'Simular Solo createUserWithPlan'}
            </button>
          </div>
        </div>

        {/* Estado del Usuario */}
        {userStatus && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Estado del Usuario</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(userStatus, null, 2)}
            </pre>
          </div>
        )}

        {/* Resultado de Simulación */}
        {result && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Resultado de Simulación</h2>
            <div className={`p-2 rounded mb-4 ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              Estado: {result.success ? 'ÉXITO' : 'ERROR'}
            </div>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
