// src/lib/services/userManagement.ts
// Servicio para gestión automatizada de usuarios y perfiles

import { SupabaseAdminService, ExtendedUserProfile, supabaseAdmin } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/lib/utils/planLimits';

export interface CreateUserRequest {
  email: string;
  name?: string;
  planId: string;
  stripeSessionId: string;
  stripeCustomerId?: string;
  amount: number;
  currency: string;
  subscriptionId?: string;
}

export interface UserActivationResult {
  success: boolean;
  userId?: string;
  profileId?: string;
  transactionId?: string;
  error?: string;
}

export class UserManagementService {
  
  /**
   * Crear usuario completo con perfil y transacción
   */
  static async createUserWithPlan(request: CreateUserRequest): Promise<UserActivationResult> {
    try {
      console.log('🚀 Iniciando creación de usuario:', request.email);

      // 1. Validar plan
      const planConfig = getPlanConfiguration(request.planId);
      if (!planConfig) {
        throw new Error(`Plan inválido: ${request.planId}`);
      }

      // 2. Verificar si ya existe una transacción para esta sesión
      const existingTransaction = await SupabaseAdminService.getTransactionBySessionId(request.stripeSessionId);
      if (existingTransaction) {
        console.log('⚠️ Transacción ya existe:', existingTransaction.id);
        return {
          success: false,
          error: 'Transacción ya procesada'
        };
      }

      // 3. Crear registro de transacción
      const transaction = await SupabaseAdminService.createStripeTransaction({
        stripe_session_id: request.stripeSessionId,
        stripe_customer_id: request.stripeCustomerId,
        user_email: request.email,
        user_name: request.name,
        plan_id: request.planId,
        amount: request.amount,
        currency: request.currency,
        payment_status: 'paid',
        subscription_id: request.subscriptionId,
        metadata: {
          created_by: 'webhook',
          plan_name: planConfig.name
        }
      });

      console.log('✅ Transacción creada:', transaction.id);

      // 4. Intentar crear invitación de usuario
      const userData = {
        name: request.name,
        plan: request.planId,
        stripe_session_id: request.stripeSessionId,
        stripe_customer_id: request.stripeCustomerId,
        transaction_id: transaction.id,
        payment_verified: true
      };

      let createdUserId: string;
      let userInvitation: any = null;

      try {
        userInvitation = await SupabaseAdminService.createUserWithInvitation(
          request.email,
          userData
        );
        createdUserId = userInvitation.user!.id;
        console.log('✅ Invitación de usuario creada:', createdUserId);

      } catch (invitationError: any) {
        if (invitationError.message && invitationError.message.includes('A user with this email address has already been registered')) {
          console.warn('⚠️ Usuario ya existe con este email. Intentando actualizar plan...');

          // Obtener el usuario existente de Supabase Auth
          const { data: { users: existingUsers }, error: fetchError } = await supabaseAdmin.auth.admin.listUsers({ email: request.email });
          if (fetchError || !existingUsers || existingUsers.length === 0) {
            console.error('❌ No se pudo obtener el usuario existente a pesar del error email_exists:', fetchError);
            throw new Error('Error obteniendo usuario existente.');
          }
          const existingAuthUser = existingUsers[0];
          createdUserId = existingAuthUser.id;

          console.log('👤 Usuario existente encontrado:', createdUserId, 'Estado:', existingAuthUser.email_confirmed_at ? 'Confirmado' : 'No Confirmado');

        } else {
          // Otro error durante la invitación
          throw invitationError;
        }
      }
      
      // 5. Crear perfil de usuario
      const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
      const isSubscription = !!request.subscriptionId;

      // Verificar si ya existe un perfil para este usuario
      const existingProfile = await SupabaseAdminService.getUserProfile(createdUserId);

      const profile: Partial<ExtendedUserProfile> = {
        user_id: createdUserId,
        subscription_plan: request.planId as 'free' | 'usuario' | 'pro',
        monthly_token_limit: getTokenLimitForPlan(request.planId),
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true,
        stripe_customer_id: request.stripeCustomerId,
        stripe_subscription_id: request.subscriptionId, // Guardar ID de suscripción
        last_payment_date: new Date().toISOString(),
        auto_renew: isSubscription,
        // Para suscripciones, plan_expires_at se establecerá en customer.subscription.created o subscription.updated
        // Para pagos únicos, establecer expiración basada en el plan
        plan_expires_at: isSubscription ? undefined : this.calculatePlanExpiration(request.planId),
        plan_features: planConfig.features,
        security_flags: {
          created_via_webhook: true,
          payment_method: 'stripe',
          activation_date: new Date().toISOString(),
          subscription_type: isSubscription ? 'recurring' : 'one_time',
          ...(existingProfile?.security_flags || {})
        }
      };
      
      const userProfile = await SupabaseAdminService.upsertUserProfile(profile);
      console.log('✅ Perfil de usuario creado/actualizado:', userProfile.id);

      // 6. Registrar cambio de plan
      await SupabaseAdminService.logPlanChange({
        user_id: createdUserId,
        old_plan: existingProfile?.subscription_plan,
        new_plan: request.planId,
        changed_by: 'system',
        reason: existingProfile ? 'Plan update via payment for existing user' : 'Initial plan assignment via payment',
        transaction_id: transaction.id
      });

      // 7. Actualizar transacción con user_id y marcar como activada
      await SupabaseAdminService.updateTransactionWithUser(transaction.id, createdUserId);
      await SupabaseAdminService.activateTransaction(transaction.id);

      console.log('🎉 Usuario creado/actualizado exitosamente:', {
        userId: createdUserId,
        profileId: userProfile.id,
        transactionId: transaction.id,
        wasExistingUser: !userInvitation
      });

      return {
        success: true,
        userId: createdUserId,
        profileId: userProfile.id,
        transactionId: transaction.id
      };
      
    } catch (error) {
      console.error('❌ Error creando usuario:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
  
  /**
   * Actualizar plan de usuario existente
   */
  static async updateUserPlan(
    userId: string, 
    newPlanId: string, 
    transactionId?: string,
    reason: string = 'Plan upgrade/downgrade'
  ): Promise<UserActivationResult> {
    try {
      console.log('🔄 Actualizando plan de usuario:', userId, 'a', newPlanId);
      
      // 1. Obtener perfil actual
      const currentProfile = await SupabaseAdminService.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('Usuario no encontrado');
      }
      
      // 2. Validar nuevo plan
      const planConfig = getPlanConfiguration(newPlanId);
      if (!planConfig) {
        throw new Error(`Plan inválido: ${newPlanId}`);
      }
      
      // 3. Actualizar perfil
      const updatedProfile: Partial<ExtendedUserProfile> = {
        ...currentProfile,
        subscription_plan: newPlanId as 'free' | 'usuario' | 'pro',
        monthly_token_limit: getTokenLimitForPlan(newPlanId),
        last_payment_date: new Date().toISOString(),
        plan_features: planConfig.features,
        updated_at: new Date().toISOString()
      };
      
      const profile = await SupabaseAdminService.upsertUserProfile(updatedProfile);
      
      // 4. Registrar cambio de plan
      await SupabaseAdminService.logPlanChange({
        user_id: userId,
        old_plan: currentProfile.subscription_plan,
        new_plan: newPlanId,
        changed_by: 'system',
        reason,
        transaction_id: transactionId
      });
      
      console.log('✅ Plan actualizado exitosamente');
      
      return {
        success: true,
        userId,
        profileId: profile.id
      };
      
    } catch (error) {
      console.error('❌ Error actualizando plan:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
  
  /**
   * Verificar estado de pago de usuario
   */
  static async verifyUserPaymentStatus(userId: string): Promise<{
    verified: boolean;
    plan: string;
    expiresAt?: string;
    lastPayment?: string;
  }> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile) {
        return { verified: false, plan: 'none' };
      }
      
      return {
        verified: profile.payment_verified,
        plan: profile.subscription_plan,
        expiresAt: profile.plan_expires_at || undefined,
        lastPayment: profile.last_payment_date || undefined
      };
      
    } catch (error) {
      console.error('Error verificando estado de pago:', error);
      return { verified: false, plan: 'error' };
    }
  }
  
  /**
   * Obtener estadísticas de usuarios
   */
  static async getUserStats(): Promise<{
    total: number;
    byPlan: Record<string, number>;
    verified: number;
    unverified: number;
  }> {
    try {
      // Esta función requeriría consultas más complejas
      // Por ahora retornamos estructura básica
      return {
        total: 0,
        byPlan: { free: 0, usuario: 0, pro: 0 },
        verified: 0,
        unverified: 0
      };
    } catch (error) {
      console.error('Error obteniendo estadísticas:', error);
      throw error;
    }
  }

  /**
   * Calcular fecha de expiración para pagos únicos y períodos de gracia
   */
  private static calculatePlanExpiration(planId: string, isGracePeriod: boolean = false): string {
    const now = new Date();

    switch (planId) {
      case 'free':
        // Plan gratuito expira en 5 días
        now.setDate(now.getDate() + 5);
        break;
      case 'usuario':
        if (isGracePeriod) {
          // Período de gracia de 7 días después de cancelar suscripción
          now.setDate(now.getDate() + 7);
        } else {
          // Pago único legacy: 30 días
          now.setDate(now.getDate() + 30);
        }
        break;
      case 'pro':
        if (isGracePeriod) {
          // Período de gracia de 14 días después de cancelar suscripción
          now.setDate(now.getDate() + 14);
        } else {
          // Pago único legacy: 30 días
          now.setDate(now.getDate() + 30);
        }
        break;
      default:
        // Por defecto, 30 días
        now.setDate(now.getDate() + 30);
        break;
    }

    return now.toISOString();
  }

  /**
   * Manejar cancelación de suscripción con período de gracia
   */
  static async handleSubscriptionCancellation(
    userId: string,
    currentPlan: string,
    subscriptionEndDate?: string,
    reason: string = 'Subscription cancelled'
  ): Promise<UserActivationResult> {
    try {
      console.log('🔄 Manejando cancelación de suscripción:', userId, 'plan:', currentPlan);

      // Obtener perfil actual
      const currentProfile = await SupabaseAdminService.getUserProfile(userId);
      if (!currentProfile) {
        throw new Error('Usuario no encontrado');
      }

      // Determinar si dar período de gracia o pasar inmediatamente a free
      let newPlan = 'free';
      let planExpiresAt: string;

      if (subscriptionEndDate) {
        // Si tenemos la fecha de fin de suscripción de Stripe, usar esa fecha
        planExpiresAt = subscriptionEndDate;
        // Mantener el plan actual hasta que expire
        newPlan = currentPlan;
      } else {
        // Sin fecha de fin, dar período de gracia basado en el plan
        if (currentPlan === 'usuario' || currentPlan === 'pro') {
          planExpiresAt = this.calculatePlanExpiration(currentPlan, true);
          newPlan = currentPlan; // Mantener plan durante período de gracia
        } else {
          // Para plan free, expirar inmediatamente
          planExpiresAt = new Date().toISOString();
          newPlan = 'free';
        }
      }

      // Actualizar perfil
      const updatedProfile: Partial<ExtendedUserProfile> = {
        ...currentProfile,
        subscription_plan: newPlan as 'free' | 'usuario' | 'pro',
        plan_expires_at: planExpiresAt,
        auto_renew: false, // Cancelar auto-renovación
        stripe_subscription_id: undefined, // Limpiar ID de suscripción
        updated_at: new Date().toISOString(),
        security_flags: {
          ...currentProfile.security_flags,
          subscription_cancelled: true,
          cancellation_date: new Date().toISOString(),
          grace_period_until: planExpiresAt
        }
      };

      const profile = await SupabaseAdminService.upsertUserProfile(updatedProfile);

      // Registrar cambio de plan
      await SupabaseAdminService.logPlanChange({
        user_id: userId,
        old_plan: currentProfile.subscription_plan,
        new_plan: newPlan,
        changed_by: 'system',
        reason: `${reason} - Grace period until ${planExpiresAt}`
      });

      console.log('✅ Cancelación de suscripción procesada con período de gracia');

      return {
        success: true,
        userId,
        profileId: profile.id
      };

    } catch (error) {
      console.error('❌ Error manejando cancelación de suscripción:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }

  /**
   * Procesar usuarios cuyo período de gracia ha expirado
   * Esta función debe ejecutarse periódicamente (ej: cron job diario)
   */
  static async processExpiredGracePeriods(): Promise<{
    processed: number;
    errors: string[];
  }> {
    try {
      console.log('🔍 Buscando usuarios con período de gracia expirado...');

      const now = new Date().toISOString();

      // Buscar usuarios con período de gracia expirado
      const { data: expiredUsers, error: searchError } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan, plan_expires_at, security_flags')
        .lt('plan_expires_at', now) // plan_expires_at < now
        .eq('auto_renew', false) // No auto-renovable (cancelado)
        .neq('subscription_plan', 'free') // No es plan gratuito
        .limit(100); // Procesar máximo 100 por vez

      if (searchError) {
        throw new Error(`Error buscando usuarios expirados: ${searchError.message}`);
      }

      if (!expiredUsers || expiredUsers.length === 0) {
        console.log('✅ No se encontraron usuarios con período de gracia expirado');
        return { processed: 0, errors: [] };
      }

      console.log(`📋 Encontrados ${expiredUsers.length} usuarios con período de gracia expirado`);

      const errors: string[] = [];
      let processed = 0;

      // Procesar cada usuario expirado
      for (const user of expiredUsers) {
        try {
          // Verificar si realmente está en período de gracia
          const isInGracePeriod = user.security_flags?.subscription_cancelled === true;

          if (!isInGracePeriod) {
            console.log(`⚠️ Usuario ${user.user_id} expirado pero no en período de gracia, omitiendo`);
            continue;
          }

          console.log(`⬇️ Degradando usuario ${user.user_id} de ${user.subscription_plan} a free`);

          // Degradar a plan gratuito
          const result = await this.updateUserPlan(
            user.user_id,
            'free',
            undefined,
            `Grace period expired - was ${user.subscription_plan}`
          );

          if (result.success) {
            processed++;
            console.log(`✅ Usuario ${user.user_id} degradado exitosamente`);
          } else {
            const errorMsg = `Error degradando usuario ${user.user_id}: ${result.error}`;
            console.error(errorMsg);
            errors.push(errorMsg);
          }

        } catch (userError) {
          const errorMsg = `Error procesando usuario ${user.user_id}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`🎯 Procesamiento completado: ${processed} usuarios degradados, ${errors.length} errores`);

      return {
        processed,
        errors
      };

    } catch (error) {
      console.error('❌ Error en processExpiredGracePeriods:', error);
      throw error;
    }
  }
}
