// src/lib/services/__tests__/userManagement.test.ts
// Tests para verificar el manejo de usuarios existentes en el flujo de pago

import { UserManagementService } from '../userManagement';
import { SupabaseAdminService } from '@/lib/supabase/admin';

// Mock de SupabaseAdminService
jest.mock('@/lib/supabase/admin', () => ({
  SupabaseAdminService: {
    createUserWithInvitation: jest.fn(),
    getUserProfile: jest.fn(),
    upsertUserProfile: jest.fn(),
    logPlanChange: jest.fn(),
    updateTransactionWithUser: jest.fn(),
    activateTransaction: jest.fn(),
    createStripeTransaction: jest.fn(),
    getTransactionBySessionId: jest.fn(),
    getUserByEmail: jest.fn(),
  },
  supabaseAdmin: {
    auth: {
      admin: {
        listUsers: jest.fn(),
      }
    },
    from: jest.fn(() => ({
      update: jest.fn(() => ({
        eq: jest.fn(() => ({ error: null }))
      }))
    }))
  }
}));

// Mock de planLimits
jest.mock('@/lib/utils/planLimits', () => ({
  getPlanConfiguration: jest.fn(() => ({
    name: 'Test Plan',
    features: ['feature1', 'feature2']
  })),
  getTokenLimitForPlan: jest.fn(() => 10000)
}));

describe('UserManagementService - Email Exists Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createUserWithPlan', () => {
    const mockRequest = {
      email: '<EMAIL>',
      name: 'Test User',
      planId: 'usuario',
      stripeSessionId: 'cs_test_123',
      stripeCustomerId: 'cus_test_123',
      amount: 999,
      currency: 'eur',
      subscriptionId: 'sub_test_123'
    };

    it('should handle email_exists error and update existing user', async () => {
      // Mock: No existing transaction
      (SupabaseAdminService.getTransactionBySessionId as jest.Mock).mockResolvedValue(null);
      
      // Mock: Transaction creation
      (SupabaseAdminService.createStripeTransaction as jest.Mock).mockResolvedValue({
        id: 'trans_123'
      });
      
      // Mock: User invitation fails with email_exists
      (SupabaseAdminService.createUserWithInvitation as jest.Mock).mockRejectedValue(
        new Error('Failed to create user invitation: A user with this email address has already been registered')
      );
      
      // Mock: Get existing user by email
      (SupabaseAdminService.getUserByEmail as jest.Mock).mockResolvedValue({
        id: 'existing_user_123',
        email: '<EMAIL>',
        email_confirmed_at: '2023-01-01T00:00:00Z'
      });
      
      // Mock: Get existing profile
      (SupabaseAdminService.getUserProfile as jest.Mock).mockResolvedValue({
        id: 'profile_123',
        user_id: 'existing_user_123',
        subscription_plan: 'free',
        security_flags: { existing: true }
      });
      
      // Mock: Profile upsert
      (SupabaseAdminService.upsertUserProfile as jest.Mock).mockResolvedValue({
        id: 'profile_123',
        user_id: 'existing_user_123'
      });
      
      // Mock: Log plan change
      (SupabaseAdminService.logPlanChange as jest.Mock).mockResolvedValue({});
      
      // Mock: Update transaction
      (SupabaseAdminService.updateTransactionWithUser as jest.Mock).mockResolvedValue(undefined);
      (SupabaseAdminService.activateTransaction as jest.Mock).mockResolvedValue(undefined);

      const result = await UserManagementService.createUserWithPlan(mockRequest);

      expect(result.success).toBe(true);
      expect(result.userId).toBe('existing_user_123');
      expect(SupabaseAdminService.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(SupabaseAdminService.getUserProfile).toHaveBeenCalledWith('existing_user_123');
      expect(SupabaseAdminService.logPlanChange).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'existing_user_123',
          old_plan: 'free',
          new_plan: 'usuario',
          reason: 'Plan update via payment for existing user'
        })
      );
    });

    it('should create new user when email does not exist', async () => {
      // Mock: No existing transaction
      (SupabaseAdminService.getTransactionBySessionId as jest.Mock).mockResolvedValue(null);
      
      // Mock: Transaction creation
      (SupabaseAdminService.createStripeTransaction as jest.Mock).mockResolvedValue({
        id: 'trans_123'
      });
      
      // Mock: User invitation succeeds
      (SupabaseAdminService.createUserWithInvitation as jest.Mock).mockResolvedValue({
        user: { id: 'new_user_123' }
      });
      
      // Mock: No existing profile
      (SupabaseAdminService.getUserProfile as jest.Mock).mockResolvedValue(null);
      
      // Mock: Profile upsert
      (SupabaseAdminService.upsertUserProfile as jest.Mock).mockResolvedValue({
        id: 'profile_123',
        user_id: 'new_user_123'
      });
      
      // Mock: Log plan change
      (SupabaseAdminService.logPlanChange as jest.Mock).mockResolvedValue({});
      
      // Mock: Update transaction
      (SupabaseAdminService.updateTransactionWithUser as jest.Mock).mockResolvedValue(undefined);
      (SupabaseAdminService.activateTransaction as jest.Mock).mockResolvedValue(undefined);

      const result = await UserManagementService.createUserWithPlan(mockRequest);

      expect(result.success).toBe(true);
      expect(result.userId).toBe('new_user_123');
      expect(SupabaseAdminService.createUserWithInvitation).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          name: 'Test User',
          plan: 'usuario',
          payment_verified: true
        })
      );
    });

    it('should handle other invitation errors properly', async () => {
      // Mock: No existing transaction
      (SupabaseAdminService.getTransactionBySessionId as jest.Mock).mockResolvedValue(null);
      
      // Mock: Transaction creation
      (SupabaseAdminService.createStripeTransaction as jest.Mock).mockResolvedValue({
        id: 'trans_123'
      });
      
      // Mock: User invitation fails with different error
      (SupabaseAdminService.createUserWithInvitation as jest.Mock).mockRejectedValue(
        new Error('Network error')
      );

      const result = await UserManagementService.createUserWithPlan(mockRequest);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
      expect(SupabaseAdminService.getUserByEmail).not.toHaveBeenCalled();
    });
  });
});
